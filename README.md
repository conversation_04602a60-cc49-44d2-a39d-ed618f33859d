# Conversor de Imagens para Web

Uma aplicação web simples para converter múltiplas imagens para formatos otimizados para web (WebP, PNG, JPEG) com controle de qualidade. Todo o processamento é feito diretamente no navegador, sem necessidade de enviar as imagens para um servidor.

## Funcionalidades

- Upload de múltiplas imagens por drag-and-drop ou seleção de arquivo
- Conversão para WebP, PNG e JPEG
- Controle de qualidade da imagem
- Preservação da orientação correta das imagens
- Visualização do resultado com informações de tamanho e dimensões
- Download das imagens convertidas
- Cópia da imagem para a área de transferência

## Tecnologias Utilizadas

- [Next.js](https://nextjs.org/) - Framework React
- [React](https://reactjs.org/) - Biblioteca JavaScript para interfaces
- [TypeScript](https://www.typescriptlang.org/) - Superset tipado de JavaScript
- [Tailwind CSS](https://tailwindcss.com/) - Framework CSS utilitário
- [Shadcn/UI](https://ui.shadcn.com/) - Componentes de UI reutilizáveis
- [browser-image-compression](https://www.npmjs.com/package/browser-image-compression) - Biblioteca para compressão de imagens no navegador
- [React Dropzone](https://react-dropzone.js.org/) - Componente para upload de arquivos

## Instalação

```bash
# Clone o repositório
git clone https://github.com/seu-usuario/conversor-imagens-web.git
cd conversor-imagens-web

# Instale as dependências
npm install

# Inicie o servidor de desenvolvimento
npm run dev
```

Acesse [http://localhost:3000](http://localhost:3000) no seu navegador para ver a aplicação.

## Como Usar

1. Arraste e solte várias imagens na área de upload ou clique para selecionar múltiplos arquivos
2. Escolha o formato de saída desejado (WebP, PNG ou JPEG)
3. Ajuste a qualidade da imagem usando o controle deslizante
4. Clique em "Converter Imagens"
5. Navegue entre as imagens convertidas usando as miniaturas
6. Visualize o resultado e compare o tamanho original com o novo tamanho
7. Baixe as imagens convertidas individualmente ou todas de uma vez

## Vantagens desta Implementação

- **Processamento no Cliente**: Todas as imagens são processadas diretamente no navegador, sem necessidade de enviar para um servidor
- **Privacidade**: Suas imagens nunca saem do seu dispositivo
- **Velocidade**: Não há tempo de upload/download, o que torna o processo mais rápido para a maioria das imagens
- **Preservação da Orientação**: A orientação correta das imagens é preservada durante a conversão

## Licença

Este projeto está licenciado sob a licença MIT - veja o arquivo [LICENSE](LICENSE) para mais detalhes.
