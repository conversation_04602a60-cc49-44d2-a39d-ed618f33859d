const withPWA = require('next-pwa');

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  eslint: {
    // Desativa a verificação do ESLint durante o build
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
      {
        protocol: 'http',
        hostname: '**',
      },
    ],
  },
  // Ignorar erros de TypeScript durante o build
  typescript: {
    ignoreBuildErrors: true,
  },
  // Configuração para ignorar erros de webpack
  webpack: (config) => {
    // Ignorar avisos de dependências críticas
    config.ignoreWarnings = [
      { module: /node_modules\/libheif-js/ },
      { module: /node_modules\/heic-convert/ },
      { module: /node_modules\/heic-decode/ }
    ];
    
    return config;
  },
};

const pwaConfig = {
  dest: 'public',
  register: true,
  skipWaiting: true,
  disable: process.env.NODE_ENV === 'development',
};

module.exports = withPWA(pwaConfig)(nextConfig); 