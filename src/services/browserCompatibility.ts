/**
 * Serviço para detectar compatibilidade do navegador com diferentes recursos
 * relacionados ao processamento de imagens
 */

export interface BrowserCompatibility {
  browser: string;
  version: string;
  supportsWebP: boolean;
  supportsAvif: boolean;
  supportsHeic: boolean;
  supportsImageBitmap: boolean;
  supportsOffscreenCanvas: boolean;
  isSafari: boolean;
  isIOS: boolean;
  isOldBrowser: boolean;
}

/**
 * Detecta o navegador atual e suas capacidades
 */
export function detectBrowserCompatibility(): BrowserCompatibility {
  // Valores padrão
  const compatibility: BrowserCompatibility = {
    browser: 'unknown',
    version: 'unknown',
    supportsWebP: false,
    supportsAvif: false,
    supportsHeic: false,
    supportsImageBitmap: false,
    supportsOffscreenCanvas: false,
    isSafari: false,
    isIOS: false,
    isOldBrowser: false
  };

  // Verificar se estamos no ambiente do navegador
  if (typeof window === 'undefined' || typeof navigator === 'undefined') {
    return compatibility;
  }

  const userAgent = navigator.userAgent;

  // Detectar Safari
  const isSafari = /^((?!chrome|android).)*safari/i.test(userAgent);
  compatibility.isSafari = isSafari;

  // Detectar iOS
  const isIOS = /iPad|iPhone|iPod/.test(userAgent) && !(window as any).MSStream;
  compatibility.isIOS = isIOS;

  // Detectar navegador e versão
  if (isSafari) {
    compatibility.browser = 'Safari';
    const versionMatch = userAgent.match(/Version\/([0-9._]+)/);
    if (versionMatch) {
      compatibility.version = versionMatch[1];
    }
  } else if (userAgent.indexOf('Firefox') > -1) {
    compatibility.browser = 'Firefox';
    const versionMatch = userAgent.match(/Firefox\/([0-9.]+)/);
    if (versionMatch) {
      compatibility.version = versionMatch[1];
    }
  } else if (userAgent.indexOf('Chrome') > -1) {
    compatibility.browser = 'Chrome';
    const versionMatch = userAgent.match(/Chrome\/([0-9.]+)/);
    if (versionMatch) {
      compatibility.version = versionMatch[1];
    }
  } else if (userAgent.indexOf('Edge') > -1 || userAgent.indexOf('Edg/') > -1) {
    compatibility.browser = 'Edge';
    const versionMatch = userAgent.match(/Edge\/([0-9.]+)/) || userAgent.match(/Edg\/([0-9.]+)/);
    if (versionMatch) {
      compatibility.version = versionMatch[1];
    }
  }

  // Verificar suporte a WebP
  compatibility.supportsWebP = testWebPSupport();

  // Verificar suporte a AVIF
  compatibility.supportsAvif = false; // Implementaremos o teste mais tarde

  // Verificar suporte a ImageBitmap
  compatibility.supportsImageBitmap = typeof window.ImageBitmap !== 'undefined';

  // Verificar suporte a OffscreenCanvas
  compatibility.supportsOffscreenCanvas = typeof (window as any).OffscreenCanvas !== 'undefined';

  // Verificar se é um navegador antigo
  // Safari < 14 não suporta WebP completamente
  if (isSafari) {
    const majorVersion = parseInt(compatibility.version.split('.')[0], 10);
    compatibility.isOldBrowser = isNaN(majorVersion) || majorVersion < 14;
  } else {
    compatibility.isOldBrowser = false;
  }

  return compatibility;
}

/**
 * Testa o suporte a WebP no navegador atual
 */
function testWebPSupport(): boolean {
  if (typeof document === 'undefined') return false;

  // Método 1: Verificar usando canvas
  try {
    const canvas = document.createElement('canvas');
    if (canvas && canvas.getContext) {
      return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    }
  } catch (e) {
    console.error('Erro ao testar suporte a WebP via canvas:', e);
  }

  // Método 2: Verificar usando detecção de navegador
  const userAgent = navigator.userAgent;

  // Chrome 32+ tem suporte a WebP
  if (userAgent.indexOf('Chrome/') > -1) {
    const match = userAgent.match(/Chrome\/([0-9]+)/);
    if (match && parseInt(match[1], 10) >= 32) {
      return true;
    }
  }

  // Edge 18+ tem suporte a WebP
  if (userAgent.indexOf('Edg/') > -1 || userAgent.indexOf('Edge/') > -1) {
    return true; // Todas as versões modernas do Edge suportam WebP
  }

  // Firefox 65+ tem suporte a WebP
  if (userAgent.indexOf('Firefox/') > -1) {
    const match = userAgent.match(/Firefox\/([0-9]+)/);
    if (match && parseInt(match[1], 10) >= 65) {
      return true;
    }
  }

  // Safari 14+ tem suporte a WebP
  if (userAgent.indexOf('Safari/') > -1 && userAgent.indexOf('Version/') > -1) {
    const match = userAgent.match(/Version\/([0-9]+)/);
    if (match && parseInt(match[1], 10) >= 14) {
      return true;
    }
  }

  return false;
}

/**
 * Obtém o formato de saída recomendado com base na compatibilidade do navegador
 */
export function getRecommendedOutputFormat(compatibility: BrowserCompatibility, userPreference: string): string {
  // Se o usuário escolheu JPEG ou PNG, respeitamos a escolha
  if (userPreference === 'jpeg' || userPreference === 'png') {
    return userPreference;
  }

  // Se o usuário escolheu WebP, verificamos a compatibilidade
  if (userPreference === 'webp') {
    if (compatibility.supportsWebP) {
      return 'webp';
    } else {
      // Fallback para JPEG se WebP não for suportado
      return 'jpeg';
    }
  }

  // Se o usuário escolheu AVIF, verificamos a compatibilidade
  if (userPreference === 'avif') {
    if (compatibility.supportsAvif) {
      return 'avif';
    } else if (compatibility.supportsWebP) {
      // Fallback para WebP se AVIF não for suportado mas WebP sim
      return 'webp';
    } else {
      // Fallback para JPEG se nem AVIF nem WebP forem suportados
      return 'jpeg';
    }
  }

  // Formato padrão se nenhuma condição acima for atendida
  return 'jpeg';
}

/**
 * Verifica se o navegador tem limitações conhecidas para processamento de imagens
 */
export function hasProcessingLimitations(compatibility: BrowserCompatibility): boolean {
  // Safari e iOS têm algumas limitações com processamento de imagens grandes
  return compatibility.isSafari || compatibility.isIOS || compatibility.isOldBrowser;
}

/**
 * Obtém mensagens de aviso específicas para o navegador atual
 */
export function getBrowserWarnings(compatibility: BrowserCompatibility): string[] {
  const warnings: string[] = [];

  if (compatibility.isOldBrowser) {
    warnings.push('Você está usando uma versão antiga do navegador que pode ter limitações com processamento de imagens.');
  }

  if (compatibility.isSafari && !compatibility.supportsWebP) {
    warnings.push('Seu navegador (Safari) tem suporte limitado ao formato WebP. Recomendamos usar JPEG para melhor compatibilidade.');
  }

  if (compatibility.isIOS) {
    warnings.push('Em dispositivos iOS, o processamento de imagens muito grandes pode ser limitado. Considere reduzir o tamanho das imagens muito grandes antes de processá-las.');
  }

  return warnings;
}
