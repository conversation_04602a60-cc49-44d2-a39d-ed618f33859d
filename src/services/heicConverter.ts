/**
 * Converte um arquivo HEIC para JPEG
 * @param file Arquivo HEIC a ser convertido
 * @returns Promise com o arquivo JPEG convertido
 */
export async function convertHeicToJpeg(file: File): Promise<File> {
  try {
    // Importar o módulo heic-convert dinamicamente apenas quando necessário
    const heicConvert = await import('heic-convert').then(module => module.default);
    
    // Converter o arquivo para ArrayBuffer
    const buffer = await file.arrayBuffer();
    const heicData = new Uint8Array(buffer);
    
    // @ts-expect-error - heic-convert tem problemas de tipagem
    const jpegData = await heicConvert({
      buffer: heicData,
      format: 'JPEG',
      quality: 0.8
    });
    
    // Criar um novo arquivo Blob com o JPEG convertido
    const jpegBlob = new Blob([jpegData], { type: 'image/jpeg' });
    
    // Criar um novo File a partir do Blob
    return new File(
      [jpegBlob], 
      file.name.replace(/\.heic$/i, '.jpg'), 
      { type: 'image/jpeg' }
    );
  } catch (error) {
    console.error('Erro ao converter HEIC para JPEG:', error);
    throw new Error(`Falha ao converter HEIC: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
  }
}

/**
 * Verifica se um arquivo é do formato HEIC
 * @param file Arquivo a ser verificado
 * @returns Verdadeiro se o arquivo for HEIC
 */
export function isHeicFile(file: File): boolean {
  return file.name.toLowerCase().endsWith('.heic') || file.type === 'image/heic';
} 