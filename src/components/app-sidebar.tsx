'use client';

import * as React from "react"
import { ImageDown } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarMenuSubButton,
} from "@/components/ui/sidebar"

// Dados de navegação do projeto
const navigationData = [
  {
    title: "Ferramentas",
    items: [
      {
        title: "Otimizador de Imagens",
        url: "/",
      },
      {
        title: "Gerador de Descrições",
        url: "/product-description",
      },
    ],
  },
]

export function AppSidebar({ className, ...props }: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname()

  return (
    <Sidebar 
      variant="floating" 
      className={`border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 h-full ${className}`}
      {...props}
    >
      <SidebarHeader className="py-4">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <Link href="/">
                <div className="bg-blue-600 dark:bg-blue-500 text-white flex aspect-square size-8 items-center justify-center rounded-lg">
                  <ImageDown className="size-4" />
                </div>
                <div className="flex flex-col gap-0.5 leading-none">
                  <span className="font-medium">PixelHunter</span>
                  <span className="text-xs text-muted-foreground">Otimizador de Imagens</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarMenu className="gap-2 px-2">
            {navigationData.map((section) => (
              <SidebarMenuItem key={section.title}>
                <SidebarMenuButton className="font-medium text-gray-700 dark:text-gray-300">
                  {section.title}
                </SidebarMenuButton>
                {section.items?.length ? (
                  <SidebarMenuSub className="ml-0 border-l-0 px-1.5">
                    {section.items.map((item) => (
                      <SidebarMenuSubItem key={item.title}>
                        <SidebarMenuSubButton 
                          asChild 
                          isActive={pathname === item.url}
                          className={pathname === item.url 
                            ? "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200" 
                            : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                          }
                        >
                          <Link href={item.url}>{item.title}</Link>
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    ))}
                  </SidebarMenuSub>
                ) : null}
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  )
}
