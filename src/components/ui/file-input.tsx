import React, { forwardRef, InputHTMLAttributes } from 'react';
import { cn } from '@/lib/utils';

export interface FileInputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'type'> {
  acceptHeic?: boolean;
}

const FileInput = forwardRef<HTMLInputElement, FileInputProps>(
  ({ className, acceptHeic = false, accept, ...props }, ref) => {
    // Adicionar extensões HEIC ao accept se necessário
    const acceptValue = acceptHeic 
      ? accept 
        ? `${accept},.heic,.HEIC` 
        : "image/*,.heic,.HEIC"
      : accept;
    
    return (
      <input
        type="file"
        className={cn(
          "block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-blue-900/20 dark:file:text-blue-400 dark:hover:file:bg-blue-800/30",
          className
        )}
        ref={ref}
        accept={acceptValue}
        {...props}
      />
    );
  }
);

FileInput.displayName = "FileInput";

export { FileInput }; 