'use client';

import { usePathname } from 'next/navigation';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

interface HeaderProps {
  className?: string;
}

export function Header({ className }: HeaderProps) {
  const pathname = usePathname();
  
  // Determinar o título da página baseado no pathname
  const getPageTitle = () => {
    switch (pathname) {
      case '/':
        return 'Otimizador de Imagens';
      case '/product-description':
        return 'Gerador de Descrições';
      default:
        return 'PixelHunter';
    }
  };

  return (
    <header className={cn("flex h-16 shrink-0 items-center gap-2 px-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 w-full", className)}>
      <SidebarTrigger className="-ml-1" />
      <Separator
        orientation="vertical"
        className="mr-2 data-[orientation=vertical]:h-4"
      />
      <div className="font-medium">{getPageTitle()}</div>
    </header>
  );
} 