'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import {
  ArrowDown,
  Download,
  Info,
  Loader,
  FileText
} from 'lucide-react';
import JSZip from 'jszip';
import Image from 'next/image';

interface ImageResult {
  originalSize: number;
  newSize: number;
  width: number;
  height: number;
  format: string;
  base64Image: string;
  fileName: string;
}

interface ConversionResultProps {
  results: ImageResult[];
}

const ConversionResult = ({ results }: ConversionResultProps) => {
  const [isDownloading, setIsDownloading] = useState(false);
  const [isZipDialogOpen, setIsZipDialogOpen] = useState(false);
  const [zipFileName, setZipFileName] = useState(`converted-images-${new Date().toISOString().slice(0, 10)}`);
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false);
  const [imageToRename, setImageToRename] = useState<ImageResult | null>(null);
  const [newImageName, setNewImageName] = useState('');
  const [customImageNames, setCustomImageNames] = useState<Record<string, string>>({});
  const [bulkRenamePrefix, setBulkRenamePrefix] = useState('image');
  const [renameMode, setRenameMode] = useState<'prefix' | 'individual'>('prefix');
  const [editableImageNames, setEditableImageNames] = useState<Record<string, string>>({});

  console.log('Componente renderizado com renameMode:', renameMode);

  // Calcular estatísticas
  const totalOriginalSize = results.reduce((acc, result) => acc + result.originalSize, 0);
  const totalNewSize = results.reduce((acc, result) => acc + result.newSize, 0);
  const savingsPercentage = Math.round((1 - (totalNewSize / totalOriginalSize)) * 100);

  // Função para baixar uma imagem com nome personalizado
  const downloadWithCustomName = (result: ImageResult, customName: string) => {
    return new Promise<void>((resolve) => {
      // Verificar se estamos no navegador
      if (typeof document === 'undefined') {
        console.error('Document não disponível');
        resolve();
        return;
      }

      // Garantir que o nome não tenha extensão duplicada
      const cleanName = customName.replace(new RegExp(`\\.${result.format}$`, 'i'), '');

      const link = document.createElement('a');
      link.href = result.base64Image;
      link.download = `${cleanName}.${result.format}`;
      document.body.appendChild(link);

      // Pequeno atraso para garantir que o navegador processe cada download
      // Usar setTimeout apenas no cliente
      if (typeof window !== 'undefined') {
        setTimeout(() => {
          link.click();
          document.body.removeChild(link);
          resolve();
        }, 100);
      } else {
        // Fallback para quando não estamos no cliente
        link.click();
        document.body.removeChild(link);
        resolve();
      }
    });
  };

  // Função para confirmar a renomeação e fazer o download
  const handleConfirmRename = async () => {
    if (!imageToRename) return;

    try {
      // Salvar o nome personalizado para uso posterior no ZIP
      setCustomImageNames(prev => ({
        ...prev,
        [imageToRename.fileName]: newImageName
      }));

      // Fazer o download com o nome personalizado
      await downloadWithCustomName(imageToRename, newImageName);
      toast.success('Imagem baixada com sucesso!');
    } catch (error) {
      console.error('Erro ao baixar imagem:', error);
      toast.error('Ocorreu um erro ao baixar a imagem.');
    } finally {
      setIsRenameDialogOpen(false);
      setImageToRename(null);
    }
  };

  // Função para abrir o modal com a imagem selecionada
  const handleImageClick = (index: number) => {
    setImageToRename(results[index]);
    const nameWithoutExtension = results[index].fileName.split('.')[0];
    // Usar o nome personalizado se existir
    const customName = customImageNames[results[index].fileName] || nameWithoutExtension;
    setNewImageName(customName);
    setIsRenameDialogOpen(true);
  };

  // Função para baixar a imagem atual
  const handleDownload = (result: ImageResult) => {
    // Não fazer o download se o diálogo de renomeação estiver aberto
    if (isRenameDialogOpen) return;

    const link = document.createElement('a');
    link.href = result.base64Image;
    const fileName = `${result.fileName.split('.')[0]}.${result.format}`;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success(`O ficheiro ${fileName} foi transferido para o seu dispositivo.`);
  };

  // Função para iniciar o processo de download em ZIP
  const handleZipDownloadClick = () => {
    setZipFileName(`converted-images-${new Date().toISOString().slice(0, 10)}`);
    setBulkRenamePrefix('image');
    setRenameMode('prefix');

    console.log('Modo de renomeação definido para:', 'prefix');

    // Inicializar os nomes editáveis com os nomes atuais ou personalizados
    const initialNames: Record<string, string> = {};
    results.forEach((result) => {
      const nameWithoutExtension = result.fileName.split('.')[0];
      initialNames[result.fileName] = customImageNames[result.fileName] || nameWithoutExtension;
    });

    console.log('Inicializando nomes editáveis:', initialNames);
    setEditableImageNames(initialNames);

    setIsZipDialogOpen(true);
  };

  // Função para baixar todas as imagens em um arquivo ZIP
  const handleDownloadAll = async (overrideNames?: Record<string, string>) => {
    if (isDownloading) return;

    try {
      setIsDownloading(true);
      setIsZipDialogOpen(false);

      // Notificar o usuário que o download começou
      const toastId = toast.loading(
        `A preparar ZIP com ${results.length} imagens. Por favor, aguarde...`
      );

      // Usar os nomes personalizados passados como parâmetro ou os do estado
      const namesToUse = overrideNames || customImageNames;

      // Log para depuração
      console.log('Nomes personalizados usados no ZIP:', namesToUse);

      // Criar um novo objeto ZIP
      const zip = new JSZip();

      // Adicionar cada imagem ao ZIP
      results.forEach((result, index) => {
        // Extrair o conteúdo base64 da imagem (remover o prefixo data:image/xxx;base64,)
        const base64Data = result.base64Image.split(',')[1];

        // Usar o nome personalizado se existir, caso contrário usar o nome original
        const customName = namesToUse[result.fileName];
        let fileName;

        if (customName) {
          // Garantir que o nome não tenha extensão duplicada
          const cleanName = customName.replace(new RegExp(`\\.${result.format}$`, 'i'), '');
          fileName = `${cleanName}.${result.format}`;
        } else {
          // Usar o nome original ou um nome padrão
          const originalName = result.fileName || `image-${index + 1}`;
          // Remover qualquer extensão existente
          const nameWithoutExtension = originalName.split('.')[0];
          fileName = `${nameWithoutExtension}.${result.format}`;
        }

        // Log para depuração
        console.log(`Adicionando ao ZIP: ${result.fileName} -> ${fileName}`);

        // Adicionar a imagem ao ZIP com o nome correto
        zip.file(fileName, base64Data, { base64: true });
      });

      // Atualizar o toast com o progresso
      toast.loading("A comprimir imagens... Isto pode demorar alguns segundos.", { id: toastId });

      // Gerar o arquivo ZIP
      const zipBlob = await zip.generateAsync({ type: 'blob' });

      // Atualizar o toast
      toast.loading("Finalizando sua obra-prima... Quase pronto!", { id: toastId });

      // Criar um URL para o blob
      const zipUrl = URL.createObjectURL(zipBlob);

      // Criar um link para download
      const link = document.createElement('a');
      link.href = zipUrl;
      link.download = `${zipFileName}.zip`;
      document.body.appendChild(link);
      link.click();

      // Limpar
      document.body.removeChild(link);
      URL.revokeObjectURL(zipUrl);

      toast.success(`${results.length} imagens otimizadas com sucesso!`, { id: toastId, duration: 4000 });
    } catch (error) {
      console.error('Erro ao criar arquivo ZIP:', error);
      toast.error('Ocorreu um erro ao criar o arquivo ZIP.');
    } finally {
      setIsDownloading(false);
    }
  };

  // Inicializar os nomes personalizados quando o componente é montado
  useEffect(() => {
    // Inicializar os nomes personalizados com os nomes originais sem extensão
    const initialNames: Record<string, string> = {};
    results.forEach((result) => {
      const nameWithoutExtension = result.fileName.split('.')[0];
      initialNames[result.fileName] = nameWithoutExtension;
    });
    setCustomImageNames(initialNames);
  }, [results]);

  // Monitorar mudanças no modo de renomeação
  useEffect(() => {
    console.log('Modo de renomeação atual:', renameMode);
  }, [renameMode]);

  return (
    <div className="p-6 space-y-6 h-full flex flex-col">
      {/* Resumo da otimização - Card compacto */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-5">
        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-3">
            <div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">Otimização concluída</h3>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Economia de espaço: <span className="text-green-600 dark:text-green-400 font-medium">{savingsPercentage}%</span>
                ({(totalOriginalSize / (1024 * 1024)).toFixed(2)} MB → {(totalNewSize / (1024 * 1024)).toFixed(2)} MB)
              </p>
              <div className="mt-2 flex items-center gap-1.5">
                <Info className="h-3.5 w-3.5 text-blue-500" />
                <span className="text-xs text-blue-600 dark:text-blue-400">
                  Imagens otimizadas com escala e qualidade ajustadas conforme suas configurações
                </span>
              </div>
            </div>
          </div>

          <Button
            onClick={handleZipDownloadClick}
            disabled={isDownloading}
            size="sm"
            className="flex items-center gap-1.5 bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-400 border border-blue-200 dark:border-blue-800 hover:bg-blue-50 dark:hover:bg-blue-900/20"
          >
            {isDownloading ? (
              <>
                <Loader className="h-3.5 w-3.5 animate-spin" />
                <span>A preparar...</span>
              </>
            ) : (
              <>
                <Download className="h-3.5 w-3.5" />
                <span>Transferir todas ({results.length})</span>
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Imagens Convertidas - Layout compacto */}
      <div className="flex-1 flex flex-col">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Imagens Otimizadas</h3>
          <div className="text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-2.5 py-1 rounded-full">
            {results.length} {results.length === 1 ? 'imagem' : 'imagens'}
          </div>
        </div>

        <div className="pr-2 flex-1">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-1 lg:grid-cols-1 xl:grid-cols-2 gap-6 pb-4">
            {results.map((result, index) => {
              const originalName = result.fileName.split('.')[0];
              const displayName = customImageNames[result.fileName] || result.fileName;

              return (
                <div
                  key={index}
                  className="bg-gray-50 dark:bg-gray-800/50 rounded-xl overflow-hidden border border-gray-100 dark:border-gray-700 hover:shadow-md transition-shadow h-full flex flex-col"
                >
                  <div
                    className="relative aspect-[16/10] bg-white dark:bg-gray-800 cursor-pointer overflow-hidden border-b border-gray-100 dark:border-gray-700"
                    onClick={() => handleImageClick(index)}
                  >
                    <div className="relative w-full h-full">
                      <Image
                        src={result.base64Image}
                        alt={`Imagem ${index + 1}`}
                        fill
                        className="object-contain"
                        unoptimized // Necessário para imagens base64
                      />
                    </div>
                    <div className="absolute top-2 right-2 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                      -{((result.originalSize - result.newSize) / result.originalSize * 100).toFixed(0)}%
                    </div>
                  </div>

                  <div className="p-4 flex-1 flex flex-col">
                    <div className="flex items-center justify-between mb-2">
                      <div className="truncate text-sm font-medium text-gray-900 dark:text-gray-100" title={displayName}>
                        {displayName}
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation();
                          setImageToRename(result);
                          setNewImageName(originalName);
                          setIsRenameDialogOpen(true);
                        }}
                        className="h-7 w-7 rounded-full"
                      >
                        <FileText className="h-3.5 w-3.5 text-gray-500" />
                        <span className="sr-only">Renomear</span>
                      </Button>
                    </div>

                    <div className="flex flex-wrap items-center gap-x-3 gap-y-1 mt-1 mb-3 text-xs text-gray-500 dark:text-gray-400">
                      <span className="bg-gray-100 dark:bg-gray-800 px-1.5 py-0.5 rounded">{result.width}×{result.height}</span>
                      <span className="flex items-center">
                        <span>{Math.round(result.originalSize / 1024)} KB</span>
                        <ArrowDown className="h-3 w-3 text-green-500 mx-0.5" />
                        <span>{Math.round(result.newSize / 1024)} KB</span>
                      </span>
                      <span className="uppercase bg-gray-100 dark:bg-gray-800 px-1.5 py-0.5 rounded">{result.format}</span>
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full h-8 text-xs mt-auto"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDownload(result);
                      }}
                    >
                      <Download className="h-3 w-3 mr-1.5" />
                      <span>Transferir</span>
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Diálogos existentes */}
      <Dialog open={isRenameDialogOpen} onOpenChange={setIsRenameDialogOpen}>
        <DialogContent className="max-w-[90vw] md:max-w-[600px] p-4 sm:p-6 overflow-hidden">
          <DialogHeader className="pb-3 space-y-1.5 border-b mb-4">
            <DialogTitle className="text-xl font-medium">Renomear Imagem</DialogTitle>
            <DialogDescription className="text-sm text-gray-500 pb-2">
              Personalize o nome antes de transferir a imagem
            </DialogDescription>
          </DialogHeader>

          {imageToRename && (
            <div className="space-y-6">
              {/* Prévia da imagem e informações */}
              <div className="flex flex-col sm:flex-row gap-4 items-center sm:items-start">
                <div
                  className="w-24 h-24 rounded-md bg-center bg-cover flex-shrink-0 border shadow-sm"
                  style={{ backgroundImage: `url(${imageToRename.base64Image})` }}
                />
                <div className="flex-1 space-y-2 text-center sm:text-left">
                  <p className="font-medium text-sm truncate" title={imageToRename.fileName}>
                    {imageToRename.fileName}
                  </p>
                  <div className="flex flex-wrap justify-center sm:justify-start gap-2 text-xs">
                    <span className="bg-gray-100 px-2 py-0.5 rounded-full">
                      {imageToRename.format.toUpperCase()}
                    </span>
                    <span className="bg-gray-100 px-2 py-0.5 rounded-full">
                      {imageToRename.width}x{imageToRename.height}
                    </span>
                    <span className="bg-gray-100 px-2 py-0.5 rounded-full">
                      {imageToRename.newSize} KB
                    </span>
                  </div>
                </div>
              </div>

              {/* Campo de entrada para o novo nome */}
              <div className="space-y-3 pt-2 border-t">
                <Label htmlFor="imageName" className="text-sm font-medium block mt-3">
                  Nome da imagem
                </Label>
                <Input
                  id="imageName"
                  value={newImageName}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewImageName(e.target.value)}
                  className="w-full mt-1.5"
                  placeholder="Digite o novo nome da imagem"
                  autoFocus
                />
                <p className="text-xs text-gray-500 flex items-center mt-2">
                  <FileText className="h-3 w-3 mr-1" aria-hidden="true" />
                  Será transferido como: <span className="font-medium ml-1">{newImageName}.{imageToRename.format}</span>
                </p>
              </div>
            </div>
          )}

          <DialogFooter className="flex flex-col-reverse sm:flex-row gap-3 sm:gap-2 pt-4 mt-2 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsRenameDialogOpen(false)}
              className="w-full sm:w-auto"
            >
              Cancelar
            </Button>
            <Button
              type="button"
              onClick={handleConfirmRename}
              className="w-full sm:w-auto flex items-center justify-center gap-1.5"
            >
              <Download className="h-4 w-4" aria-hidden="true" />
              Transferir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para configurar o download em ZIP */}
      <Dialog open={isZipDialogOpen} onOpenChange={setIsZipDialogOpen}>
        <DialogContent
          className="sm:max-w-[600px] p-0 max-h-[90vh] flex flex-col overflow-hidden will-change-transform transform-gpu"
          style={{
            WebkitFontSmoothing: 'antialiased',
            MozOsxFontSmoothing: 'grayscale',
            textRendering: 'optimizeLegibility'
          }}
        >
          <div className="px-6 pt-6 pb-2">
            <DialogTitle className="text-xl font-bold text-rendering-optimizeLegibility">Opções de Transferência em ZIP</DialogTitle>
            <DialogDescription className="text-gray-500 mt-1 text-rendering-optimizeLegibility">
              Configure como deseja transferir as {results.length} imagens num único ficheiro ZIP.
            </DialogDescription>
          </div>

          <div className="px-6 py-4 overflow-y-auto flex-1 space-y-6">
            {/* Seção 1: Nome do arquivo ZIP */}
            <div className="space-y-2">
              <Label htmlFor="zipFileName" className="text-sm font-medium block text-rendering-optimizeLegibility">
                Nome do ficheiro ZIP
              </Label>
              <Input
                id="zipFileName"
                value={zipFileName}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setZipFileName(e.target.value)}
                className="w-full"
                placeholder="Nome do ficheiro ZIP"
              />
              <p className="text-xs text-gray-500 text-rendering-optimizeLegibility">O ficheiro será transferido como: {zipFileName}.zip</p>
            </div>

            {/* Seção 2: Método de renomeação */}
            <div className="space-y-3 border rounded-lg p-4 bg-gray-50 dark:bg-gray-800/50">
              <h3 className="text-sm font-medium text-rendering-optimizeLegibility">Método de renomeação das imagens</h3>

              <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="prefixMode"
                    name="renameMode"
                    checked={renameMode === 'prefix'}
                    onChange={() => {
                      console.log('Alterando modo para: prefix');
                      setRenameMode('prefix');
                    }}
                    className="h-4 w-4 text-primary"
                  />
                  <Label htmlFor="prefixMode" className="cursor-pointer text-rendering-optimizeLegibility">Usar prefixo comum</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="individualMode"
                    name="renameMode"
                    checked={renameMode === 'individual'}
                    onChange={() => {
                      console.log('Alterando modo para: individual');
                      setRenameMode('individual');
                    }}
                    className="h-4 w-4 text-primary"
                  />
                  <Label htmlFor="individualMode" className="cursor-pointer text-rendering-optimizeLegibility">Personalizar cada nome</Label>
                </div>
              </div>
            </div>

            {/* Conteúdo baseado no modo selecionado */}
            <div className="border rounded-lg p-4">
              {renameMode === 'prefix' ? (
                <div className="space-y-4">
                  <div className="bg-blue-50 text-blue-700 px-3 py-2 rounded-md text-sm flex items-center">
                    <Info className="h-4 w-4 mr-2" aria-hidden="true" />
                    Modo selecionado: <strong className="ml-1">Prefixo comum</strong>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bulkRenamePrefix" className="text-sm font-medium block">
                      Prefixo para todas as imagens
                    </Label>
                    <Input
                      id="bulkRenamePrefix"
                      value={bulkRenamePrefix}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setBulkRenamePrefix(e.target.value)}
                      className="w-full"
                      placeholder="Ex: imagem"
                    />
                    <p className="text-xs text-gray-500">
                      As imagens serão nomeadas como: <span className="font-medium">{bulkRenamePrefix}-1.jpg, {bulkRenamePrefix}-2.png, etc.</span>
                    </p>
                  </div>

                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-2">Prévia dos nomes:</h4>
                    <div className="max-h-32 overflow-y-auto bg-gray-50 rounded-md p-2">
                      {results.slice(0, 5).map((result, index) => (
                        <div key={index} className="flex items-center gap-2 mb-1">
                          <span className="text-xs bg-white px-2 py-1 rounded border">{`${bulkRenamePrefix}-${index + 1}.${result.format}`}</span>
                          <span className="text-xs text-gray-400">({result.fileName})</span>
                        </div>
                      ))}
                      {results.length > 5 && (
                        <p className="text-xs text-gray-400 mt-1">+ {results.length - 5} mais imagens...</p>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="bg-blue-50 text-blue-700 px-3 py-2 rounded-md text-sm flex items-center">
                    <Info className="h-4 w-4 mr-2" aria-hidden="true" />
                    Modo selecionado: <strong className="ml-1">Personalização individual</strong>
                  </div>

                  <p className="text-sm">Personalize o nome de cada imagem individualmente:</p>

                  <div className="border rounded-md overflow-hidden">
                    <div className="grid grid-cols-12 bg-gray-100 p-2 text-xs font-medium">
                      <div className="col-span-1 flex items-center justify-center">#</div>
                      <div className="col-span-5">Nome original</div>
                      <div className="col-span-6">Nome personalizado</div>
                    </div>

                    <div className="max-h-[250px] overflow-y-auto">
                      {results.map((result, index) => (
                        <div key={index} className="grid grid-cols-12 p-2 items-center border-t hover:bg-gray-50">
                          <div className="col-span-1 flex items-center justify-center">
                            <div
                              className="w-8 h-8 rounded-md bg-center bg-cover"
                              style={{ backgroundImage: `url(${result.base64Image})` }}
                            />
                          </div>
                          <div className="col-span-5 truncate text-sm px-2" title={result.fileName}>
                            {result.fileName}
                          </div>
                          <div className="col-span-6">
                            <Input
                              value={editableImageNames[result.fileName] || ''}
                              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                setEditableImageNames(prev => ({
                                  ...prev,
                                  [result.fileName]: e.target.value
                                }));
                              }}
                              className="h-8 text-sm"
                              placeholder={result.fileName.split('.')[0]}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Resumo */}
            <div className="bg-gray-50 rounded-lg p-4 space-y-2">
              <h3 className="text-sm font-medium">Resumo da transferência</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Total de imagens:</p>
                  <p className="font-medium">{results.length} imagens</p>
                </div>
                <div>
                  <p className="text-gray-500">Tamanho total:</p>
                  <p className="font-medium">{Math.round(totalNewSize / 1024)} KB</p>
                </div>
              </div>
            </div>
          </div>

          <div className="border-t px-6 py-4 bg-white dark:bg-gray-800 sticky bottom-0 left-0 right-0 mt-auto">
            <div className="flex flex-col sm:flex-row gap-2 justify-end">
              <Button type="button" variant="outline" onClick={() => setIsZipDialogOpen(false)} className="w-full sm:w-auto">
                Cancelar
              </Button>
              <Button
                type="button"
                onClick={() => {
                  // Preparar nomes personalizados para todas as imagens
                  const newCustomNames: Record<string, string> = { ...customImageNames };
                  console.log('Estado atual dos nomes personalizados:', customImageNames);

                  if (renameMode === 'prefix') {
                    // Usar prefixo comum
                    console.log(`Usando modo de prefixo com: "${bulkRenamePrefix}"`);
                    results.forEach((result, index) => {
                      // Garantir que o nome não tenha extensão duplicada
                      const prefixName = `${bulkRenamePrefix}-${index + 1}`;
                      newCustomNames[result.fileName] = prefixName;
                      console.log(`Definindo nome para ${result.fileName}: ${prefixName}`);
                    });
                  } else {
                    // Usar nomes personalizados individuais
                    results.forEach((result) => {
                      if (editableImageNames[result.fileName] && editableImageNames[result.fileName].trim() !== '') {
                        newCustomNames[result.fileName] = editableImageNames[result.fileName].trim();
                        console.log(`Definindo nome personalizado para ${result.fileName}: ${editableImageNames[result.fileName].trim()}`);
                      } else if (!newCustomNames[result.fileName]) {
                        // Se não houver um nome personalizado, usar o nome original sem extensão
                        const nameWithoutExtension = result.fileName.split('.')[0];
                        newCustomNames[result.fileName] = nameWithoutExtension;
                        console.log(`Usando nome original para ${result.fileName}: ${nameWithoutExtension}`);
                      }
                    });
                  }

                  console.log('Novos nomes personalizados:', newCustomNames);

                  // Atualizar o estado global de nomes personalizados e iniciar o download
                  setCustomImageNames(newCustomNames);

                  // Iniciar o download com os novos nomes diretamente
                  handleDownloadAll(newCustomNames);
                }}
                disabled={isDownloading}
                className="w-full sm:w-auto flex items-center justify-center gap-2"
              >
                {isDownloading ? (
                  <>
                    <Loader className="h-4 w-4 animate-spin" aria-hidden="true" />
                    A preparar...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4" aria-hidden="true" />
                    Transferir ZIP
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ConversionResult;