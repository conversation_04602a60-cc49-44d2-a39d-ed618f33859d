'use client';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';
import { Loader, ImageDown, Info } from 'lucide-react';
import { BrowserCompatibility } from '@/services/browserCompatibility';

interface ConversionSettingsProps {
  format: string;
  quality: number;
  resizePercentage: number;
  onFormatChange: (format: string) => void;
  onQualityChange: (quality: number) => void;
  onResizeChange: (resizePercentage: number) => void;
  onConvert: () => void;
  isConverting: boolean;
  hasImages: boolean;
  imageCount: number;
  browserCompatibility?: BrowserCompatibility | null;
}

const ConversionSettings = ({
  format,
  quality,
  resizePercentage,
  onFormatChange,
  onQualityChange,
  onResizeChange,
  onConvert,
  isConverting,
  hasImages,
  imageCount,
  browserCompatibility
}: ConversionSettingsProps) => {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2 mb-2">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">Definições de Conversão</h3>
      </div>

      <div className="space-y-6">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
          </div>
          <Select
            value={format}
            onValueChange={onFormatChange}
            disabled={!hasImages || isConverting}
          >
            <SelectTrigger id="format" className="w-full h-11 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <SelectValue placeholder="Selecione um formato" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="jpeg">JPEG</SelectItem>
              <SelectItem value="png">PNG</SelectItem>
              <SelectItem value="webp">WebP</SelectItem>
            </SelectContent>
          </Select>

          {browserCompatibility && !browserCompatibility.supportsWebP && format === 'webp' && (
            <div className="mt-2 text-xs text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20 p-2 rounded-md flex items-start gap-1.5">
              <Info className="h-3.5 w-3.5 mt-0.5 flex-shrink-0" />
              <p>Seu navegador ({browserCompatibility.browser}) pode ter compatibilidade limitada com WebP. JPEG é recomendado para melhor compatibilidade.</p>
            </div>
          )}
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label htmlFor="quality" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Qualidade: {quality}%
            </label>
            <span className="text-xs text-gray-500 dark:text-gray-400 px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded-full">
              {quality < 20 ? 'Muito baixa' : quality < 40 ? 'Baixa' : quality < 70 ? 'Média' : 'Alta'}
            </span>
          </div>
          <div className="pt-2 px-1">
            <Slider
              id="quality"
              value={[quality]}
              min={1}
              max={100}
              step={1}
              onValueChange={(value) => onQualityChange(value[0])}
              disabled={!hasImages || isConverting}
              className="py-1"
            />
          </div>
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1 px-1">
            <span>Menor tamanho</span>
            <span>Melhor qualidade</span>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label htmlFor="resize" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Escala da imagem: {resizePercentage}%
            </label>
            <span className="text-xs text-gray-500 dark:text-gray-400 px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded-full">
              {resizePercentage >= 100 ? 'Tamanho original' : `Reduzir para ${resizePercentage}%`}
            </span>
          </div>
          <div className="pt-2 px-1">
            <Slider
              id="resize"
              value={[resizePercentage]}
              min={10}
              max={100}
              step={5}
              onValueChange={(value) => onResizeChange(value[0])}
              disabled={!hasImages || isConverting}
              className="py-1"
            />
          </div>
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1 px-1">
            <span>Menor tamanho</span>
            <span>Tamanho original</span>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 px-1">
            {resizePercentage >= 100
              ? 'As dimensões originais das imagens serão mantidas, apenas a qualidade será otimizada'
              : `As imagens serão redimensionadas para ${resizePercentage}% do tamanho original (mantendo proporções)`}
          </p>
        </div>

        <Button
          onClick={onConvert}
          disabled={!hasImages || isConverting}
          className="w-full h-12 mt-4 text-base"
        >
          {isConverting ? (
            <>
              <Loader className="mr-2 h-4 w-4 animate-spin" />
              <span>A processar {imageCount} {imageCount === 1 ? 'imagem' : 'imagens'}...</span>
            </>
          ) : (
            <>
              <ImageDown className="mr-2 h-5 w-5" />
              <span>Otimizar {imageCount} {imageCount === 1 ? 'imagem' : 'imagens'}</span>
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default ConversionSettings;