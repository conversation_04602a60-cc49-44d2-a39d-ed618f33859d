'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';

interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

const InstallPWA = () => {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isInstallable, setIsInstallable] = useState(false);
  const [isIOS, setIsIOS] = useState(false);

  useEffect(() => {
    // Detectar se é iOS
    const isIOSDevice = /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window);
    setIsIOS(isIOSDevice);

    // Capturar o evento beforeinstallprompt
    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevenir o comportamento padrão do Chrome
      e.preventDefault();
      // Armazenar o evento para uso posterior
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      // Mostrar o botão de instalação
      setIsInstallable(true);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    // Verificar se o app já está instalado
    window.addEventListener('appinstalled', () => {
      setIsInstallable(false);
      setDeferredPrompt(null);
    });

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', () => {});
    };
  }, []);

  const handleInstallClick = async () => {
    if (!deferredPrompt) return;

    // Mostrar o prompt de instalação
    await deferredPrompt.prompt();

    // Aguardar a escolha do usuário
    const choiceResult = await deferredPrompt.userChoice;
    
    if (choiceResult.outcome === 'accepted') {
      console.log('Usuário aceitou a instalação do PWA');
    } else {
      console.log('Usuário recusou a instalação do PWA');
    }
    
    // Limpar o prompt armazenado
    setDeferredPrompt(null);
    setIsInstallable(false);
  };

  if (!isInstallable && !isIOS) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {isInstallable && (
        <Button 
          onClick={handleInstallClick}
          className="flex items-center gap-2 bg-primary hover:bg-primary/90"
        >
          <Download size={16} />
          Instalar App
        </Button>
      )}
      
      {isIOS && !isInstallable && (
        <div className="bg-white dark:bg-slate-800 p-3 rounded-lg shadow-lg max-w-xs">
          <p className="text-sm mb-2">
            Para instalar este app no seu iPhone:
          </p>
          <ol className="text-xs list-decimal pl-4 space-y-1">
            <li>Toque no ícone de compartilhamento</li>
            <li>Role para baixo e toque em &quot;Adicionar à Tela de Início&quot;</li>
          </ol>
        </div>
      )}
    </div>
  );
};

export default InstallPWA; 