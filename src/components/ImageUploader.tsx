'use client';

import { useCallback, useState, useEffect, useRef } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Trash2, Plus, ImagePlus, FileIcon, X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { FileInput } from '@/components/ui/file-input';

interface ImageUploaderProps {
  onImageSelect: (files: File[]) => void;
  selectedFiles: File[];
}

const ImageUploader = ({ onImageSelect, selectedFiles = [] }: ImageUploaderProps) => {
  // Estado para controlar se estamos no cliente
  const [isMounted, setIsMounted] = useState(false);

  // Efeito para marcar quando o componente está montado no cliente
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    // Verificar se são imagens ou arquivos HEIC
    const imageFiles = acceptedFiles.filter(file =>
      file.type.startsWith('image/') ||
      file.name.toLowerCase().endsWith('.heic')
    );

    if (imageFiles.length === 0) {
      toast.error('Por favor, selecione apenas ficheiros de imagem.');
      return;
    }

    // Passar os arquivos para o componente pai
    onImageSelect(imageFiles);

    // Mostrar mensagem de sucesso
    toast.success(`${imageFiles.length} ${imageFiles.length === 1 ? 'imagem adicionada' : 'imagens adicionadas'} com sucesso!`);
  }, [onImageSelect]);

  // Usar useDropzone diretamente, mas só renderizar seus efeitos no cliente
  const { getRootProps, getInputProps, isDragActive, open } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp', '.heic', '.HEIC']
    },
    multiple: true,
    // Desabilitar no servidor para evitar erros de hidratação
    disabled: !isMounted,
    noClick: false,
    noKeyboard: false
  });

  // Referência para o input de arquivo personalizado
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Referência para o input do dropzone
  const dropzoneInputRef = useRef<HTMLInputElement>(null);

  // Função para abrir o seletor de arquivos personalizado
  const handleCustomFileSelect = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Função para processar arquivos selecionados pelo input personalizado
  const handleCustomFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files);

      // Verificar se são imagens ou arquivos HEIC
      const imageFiles = files.filter(file =>
        file.type.startsWith('image/') ||
        file.name.toLowerCase().endsWith('.heic')
      );

      if (imageFiles.length === 0) {
        toast.error('Por favor, selecione apenas ficheiros de imagem.');
        return;
      }

      // Passar os arquivos diretamente para o componente pai
      onImageSelect(imageFiles);

      // Mostrar mensagem de sucesso
      toast.success(`${imageFiles.length} ${imageFiles.length === 1 ? 'imagem adicionada' : 'imagens adicionadas'} com sucesso!`);
    }
  };

  const handleReset = () => {
    // Clear the parent component's state
    onImageSelect([]);

    // Reset the file input value
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    // Reset the dropzone input element using our reference
    if (dropzoneInputRef.current) {
      dropzoneInputRef.current.value = '';
    }

    // Criar um novo evento de input para garantir que os listeners sejam acionados
    const event = new Event('input', { bubbles: true });
    if (fileInputRef.current) {
      fileInputRef.current.dispatchEvent(event);
    }
    if (dropzoneInputRef.current) {
      dropzoneInputRef.current.dispatchEvent(event);
    }

    toast.info('Seleção de imagens limpa');
  };

  // Função para formatar o tamanho do arquivo
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };

  // Função para remover um arquivo específico
  const removeFile = (index: number) => {
    const newFiles = [...selectedFiles];
    newFiles.splice(index, 1);
    onImageSelect(newFiles);
    toast.info('Imagem removida');
  };

  // Renderizar uma versão simplificada no servidor para evitar erros de hidratação
  if (!isMounted) {
    return (
      <div className="w-full">
        <div className="border-2 border-dashed rounded-lg p-10 text-center cursor-pointer transition-colors border-gray-300 bg-gray-50 dark:bg-gray-800/50">
          <div className="flex flex-col items-center justify-center gap-4">
            <div className="bg-white dark:bg-gray-700 p-4 rounded-full">
              <ImagePlus className="h-10 w-10 text-gray-400 dark:text-gray-300" />
            </div>
            <div className="text-center">
              <p className="text-base font-medium text-gray-700 dark:text-gray-200">
                Arraste e largue várias imagens aqui
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                ou clique para selecionar
              </p>
            </div>
            <p className="text-xs text-gray-400 dark:text-gray-500 mt-2">
              Formatos suportados: JPG, PNG, WebP, HEIC
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-4">
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-10 text-center cursor-pointer transition-colors ${
          isDragActive
            ? 'border-blue-400 bg-blue-50 dark:border-blue-500 dark:bg-blue-900/20'
            : 'border-gray-300 hover:border-blue-300 bg-gray-50 dark:bg-gray-800/50 dark:border-gray-600 dark:hover:border-blue-700'
        }`}
      >
        <input {...getInputProps()} ref={dropzoneInputRef} />
        <div className="flex flex-col items-center justify-center gap-4">
          <div className={`p-4 rounded-full ${isDragActive ? 'bg-blue-100 dark:bg-blue-800/30' : 'bg-white dark:bg-gray-700'}`}>
            <ImagePlus className={`h-10 w-10 ${isDragActive ? 'text-blue-500 dark:text-blue-400' : 'text-gray-400 dark:text-gray-300'}`} />
          </div>
          <div className="text-center">
            {isDragActive ? (
              <p className="text-base font-medium text-blue-600 dark:text-blue-400">
                Solte as imagens aqui...
              </p>
            ) : (
              <>
                <p className="text-base font-medium text-gray-700 dark:text-gray-200">
                  Arraste e largue várias imagens aqui
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                  ou <span
                    className="text-blue-600 dark:text-blue-400 hover:underline cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      // Resetar o valor do input antes de abrir o seletor
                      if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                      }
                      handleCustomFileSelect();
                    }}
                  >clique para selecionar</span>
                </p>
              </>
            )}
          </div>
          <p className="text-xs text-gray-400 dark:text-gray-500 mt-2">
            Formatos suportados: JPG, PNG, WebP, HEIC
          </p>
        </div>
      </div>

      {/* Input de arquivo personalizado escondido */}
      <div className="hidden">
        <FileInput
          ref={fileInputRef}
          multiple
          acceptHeic={true}
          onChange={handleCustomFileChange}
        />
      </div>

      {selectedFiles.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Badge variant="outline" className="px-2.5 py-1 text-xs font-medium bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800">
              {selectedFiles.length} {selectedFiles.length === 1 ? 'imagem selecionada' : 'imagens selecionadas'}
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              className="text-xs h-8 px-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 border-red-200 dark:border-red-800"
            >
              <Trash2 className="h-3.5 w-3.5 mr-1" />
              Limpar tudo
            </Button>
          </div>

          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
            <div className="max-h-[200px] overflow-y-auto">
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="bg-gray-50 dark:bg-gray-900/50 text-xs">
                    <tr>
                      <th className="px-4 py-2 text-left font-medium text-gray-500 dark:text-gray-400">Nome</th>
                      <th className="px-4 py-2 text-right font-medium text-gray-500 dark:text-gray-400 w-24">Tamanho</th>
                      <th className="px-4 py-2 text-right font-medium text-gray-500 dark:text-gray-400 w-16">Ação</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    {selectedFiles.map((file, index) => (
                      <tr key={`${file.name}-${index}`} className="hover:bg-gray-50 dark:hover:bg-gray-800/50">
                        <td className="px-4 py-2 flex items-center gap-2">
                          <FileIcon className="h-4 w-4 text-gray-400 dark:text-gray-500 flex-shrink-0" />
                          <span className="truncate" title={file.name}>{file.name}</span>
                        </td>
                        <td className="px-4 py-2 text-right text-gray-500 dark:text-gray-400">
                          {formatFileSize(file.size)}
                        </td>
                        <td className="px-4 py-2 text-right">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={(e) => {
                              e.stopPropagation();
                              removeFile(index);
                            }}
                            className="h-7 w-7 rounded-full hover:bg-red-50 dark:hover:bg-red-900/20 text-destructive-foreground"
                          >
                            <X className="h-3.5 w-3.5 mr-1" />
                            <span className="sr-only">Remover</span>
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <Button
            onClick={(e) => {
              e.preventDefault();
              // Resetar o valor do input antes de abrir o seletor
              if (dropzoneInputRef.current) {
                dropzoneInputRef.current.value = '';
              }
              // Usar o método open do dropzone
              open();
            }}
            variant="outline"
            size="sm"
            className="w-full h-9 border-dashed border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-300"
          >
            <Plus className="h-3.5 w-3.5 mr-1.5" />
            <span>Adicionar mais imagens</span>
          </Button>
        </div>
      )}
    </div>
  );
};

export default ImageUploader;