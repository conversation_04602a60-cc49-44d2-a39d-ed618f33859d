import sharp from 'sharp';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Obter o diretório atual no formato ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

// Certifique-se de que o diretório de ícones existe
const iconsDir = path.join(rootDir, 'public', 'icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// Configurações de cores - Cores fluorescentes
const backgroundColors = [
  '#39FF14', // Verde fluorescente
  '#FF3131', // Vermelho fluorescente
  '#FF00FF', // Magenta fluorescente
  '#00FFFF', // Ciano fluorescente
  '#FFFF00', // Amarelo fluorescente
];

// Função para criar um ícone quadrado com um globo no centro
async function generateIcon(size, outputName, isApple = false) {
  try {
    // Calcular o tamanho do ícone do globo (70% do tamanho total)
    const iconSize = Math.round(size * 0.7);
    const padding = Math.round((size - iconSize) / 2);
    
    // Escolher uma cor fluorescente aleatória para o fundo
    const backgroundColor = backgroundColors[Math.floor(Math.random() * backgroundColors.length)];
    
    // Criar um fundo quadrado com a cor fluorescente
    const background = Buffer.from(
      `<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
        <rect width="${size}" height="${size}" rx="${isApple ? size / 4 : size / 8}" fill="${backgroundColor}"/>
      </svg>`
    );

    // Processar o SVG do globo e torná-lo preto
    const blackGlobe = Buffer.from(
      `<svg fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><g clip-path="url(#a)"><path fill-rule="evenodd" clip-rule="evenodd" d="M10.27 14.1a6.5 6.5 0 0 0 3.67-3.45q-1.24.21-2.7.34-.31 1.83-.97 3.1M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16m.48-1.52a7 7 0 0 1-.96 0H7.5a4 4 0 0 1-.84-1.32q-.38-.89-.63-2.08a40 40 0 0 0 3.92 0q-.25 1.2-.63 2.08a4 4 0 0 1-.84 1.31zm2.94-4.76q1.66-.15 2.95-.43a7 7 0 0 0 0-2.58q-1.3-.27-2.95-.43a18 18 0 0 1 0 3.44m-1.27-3.54a17 17 0 0 1 0 3.64 39 39 0 0 1-4.3 0 17 17 0 0 1 0-3.64 39 39 0 0 1 4.3 0m1.1-1.17q1.45.13 2.69.34a6.5 6.5 0 0 0-3.67-3.44q.65 1.26.98 3.1M8.48 1.5l.01.02q.41.37.84 **********.63 2.08a40 40 0 0 0-3.92 0q.25-1.2.63-2.08a4 4 0 0 1 .85-1.32 7 7 0 0 1 .96 0m-2.75.4a6.5 6.5 0 0 0-3.67 3.44 29 29 0 0 1 2.7-.34q.31-1.83.97-3.1M4.58 6.28q-1.66.16-2.95.43a7 7 0 0 0 0 2.58q1.3.27 2.95.43a18 18 0 0 1 0-3.44m.17 4.71q-1.45-.12-2.69-.34a6.5 6.5 0 0 0 3.67 3.44q-.65-1.27-.98-3.1" fill="#000"/></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h16v16H0z"/></clipPath></defs></svg>`
    );

    // Redimensionar o globo preto
    const globeBuffer = await sharp(blackGlobe)
      .resize(iconSize, iconSize)
      .toBuffer();

    // Sobrepor o globo no fundo
    await sharp(background)
      .composite([
        {
          input: globeBuffer,
          top: padding,
          left: padding,
        },
      ])
      .toFile(path.join(iconsDir, outputName));

    console.log(`✅ Gerado: ${outputName}`);
  } catch (error) {
    console.error(`❌ Erro ao gerar ${outputName}:`, error);
  }
}

// Gerar ícones para o PWA
async function generateIcons() {
  console.log('🔄 Gerando ícones para PWA...');

  // Ícones para o manifest.json
  await generateIcon(192, 'icon-192x192.png');
  await generateIcon(384, 'icon-384x384.png');
  await generateIcon(512, 'icon-512x512.png');
  
  // Ícone para Apple
  await generateIcon(180, 'apple-icon-180x180.png', true);

  console.log('✨ Todos os ícones foram gerados com sucesso!');
}

// Executar a função principal
generateIcons(); 