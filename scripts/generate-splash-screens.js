import sharp from 'sharp';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Obter o diretório atual no formato ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

// Certifique-se de que o diretório de splash screens existe
const splashDir = path.join(rootDir, 'public', 'splash');
if (!fs.existsSync(splashDir)) {
  fs.mkdirSync(splashDir, { recursive: true });
}

// Configurações de cores - Cores fluorescentes
const backgroundColors = [
  '#39FF14', // Verde fluorescente
  '#FF3131', // Vermelho fluorescente
  '#FF00FF', // Magenta fluorescente
  '#00FFFF', // Ciano fluorescente
  '#FFFF00', // Amarelo fluorescente
];
const textColor = '#000000'; // Texto preto

// Configurações de dispositivos iOS (width, height, deviceName)
const iosDevices = [
  { width: 1290, height: 2796, name: 'iPhone 14 Pro Max' },
  { width: 1179, height: 2556, name: 'iPhone 14 Pro' },
  { width: 1170, height: 2532, name: 'iPhone 13/14' },
  { width: 1284, height: 2778, name: 'iPhone 12 Pro Max' },
  { width: 1125, height: 2436, name: 'iPhone X/XS' },
  { width: 828, height: 1792, name: 'iPhone XR' },
  { width: 1242, height: 2688, name: 'iPhone XS Max' },
  { width: 750, height: 1334, name: 'iPhone 8/SE' },
  { width: 1242, height: 2208, name: 'iPhone 8 Plus' },
  { width: 1620, height: 2160, name: 'iPad Pro 10.5"' },
  { width: 1668, height: 2224, name: 'iPad Pro 11"' },
  { width: 2048, height: 2732, name: 'iPad Pro 12.9"' },
];

// Função para criar uma splash screen
async function generateSplashScreen(width, height, deviceName) {
  try {
    // Calcular o tamanho do ícone do globo (30% da menor dimensão)
    const iconSize = Math.round(Math.min(width, height) * 0.3);
    
    // Escolher uma cor fluorescente aleatória para o fundo
    const backgroundColor = backgroundColors[Math.floor(Math.random() * backgroundColors.length)];
    
    // Criar um fundo com a cor fluorescente
    const background = Buffer.from(
      `<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="${width}" height="${height}" fill="${backgroundColor}"/>
        <text x="${width/2}" y="${height/2 + iconSize/2 + 60}" 
              font-family="Arial, sans-serif" font-size="48" 
              fill="${textColor}" text-anchor="middle" 
              font-weight="bold">
          Image Optimizer
        </text>
      </svg>`
    );

    // Processar o SVG do globo e torná-lo preto
    const blackGlobe = Buffer.from(
      `<svg fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><g clip-path="url(#a)"><path fill-rule="evenodd" clip-rule="evenodd" d="M10.27 14.1a6.5 6.5 0 0 0 3.67-3.45q-1.24.21-2.7.34-.31 1.83-.97 3.1M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16m.48-1.52a7 7 0 0 1-.96 0H7.5a4 4 0 0 1-.84-1.32q-.38-.89-.63-2.08a40 40 0 0 0 3.92 0q-.25 1.2-.63 2.08a4 4 0 0 1-.84 1.31zm2.94-4.76q1.66-.15 2.95-.43a7 7 0 0 0 0-2.58q-1.3-.27-2.95-.43a18 18 0 0 1 0 3.44m-1.27-3.54a17 17 0 0 1 0 3.64 39 39 0 0 1-4.3 0 17 17 0 0 1 0-3.64 39 39 0 0 1 4.3 0m1.1-1.17q1.45.13 2.69.34a6.5 6.5 0 0 0-3.67-3.44q.65 1.26.98 3.1M8.48 1.5l.01.02q.41.37.84 **********.63 2.08a40 40 0 0 0-3.92 0q.25-1.2.63-2.08a4 4 0 0 1 .85-1.32 7 7 0 0 1 .96 0m-2.75.4a6.5 6.5 0 0 0-3.67 3.44 29 29 0 0 1 2.7-.34q.31-1.83.97-3.1M4.58 6.28q-1.66.16-2.95.43a7 7 0 0 0 0 2.58q1.3.27 2.95.43a18 18 0 0 1 0-3.44m.17 4.71q-1.45-.12-2.69-.34a6.5 6.5 0 0 0 3.67 3.44q-.65-1.27-.98-3.1" fill="#000"/></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h16v16H0z"/></clipPath></defs></svg>`
    );

    // Redimensionar o globo preto
    const globeBuffer = await sharp(blackGlobe)
      .resize(iconSize, iconSize)
      .toBuffer();

    // Sobrepor o globo no fundo
    await sharp(background)
      .composite([
        {
          input: globeBuffer,
          top: Math.round(height/2 - iconSize/2 - 40),
          left: Math.round(width/2 - iconSize/2),
        },
      ])
      .toFile(path.join(splashDir, `splash-${width}x${height}.png`));

    console.log(`✅ Gerado: splash-${width}x${height}.png para ${deviceName}`);
  } catch (error) {
    console.error(`❌ Erro ao gerar splash screen para ${deviceName}:`, error);
  }
}

// Gerar splash screens para iOS
async function generateSplashScreens() {
  console.log('🔄 Gerando splash screens para iOS...');

  for (const device of iosDevices) {
    await generateSplashScreen(device.width, device.height, device.name);
  }

  // Gerar o HTML para incluir no layout
  generateSplashScreenHTML();

  console.log('✨ Todas as splash screens foram geradas com sucesso!');
}

// Gerar o HTML para incluir no layout
function generateSplashScreenHTML() {
  let html = '<!-- Splash Screens para iOS -->\n';
  
  for (const device of iosDevices) {
    html += `<link rel="apple-touch-startup-image" media="screen and (device-width: ${device.width/3}px) and (device-height: ${device.height/3}px) and (-webkit-device-pixel-ratio: 3)" href="/splash/splash-${device.width}x${device.height}.png" />\n`;
  }
  
  // Salvar o HTML em um arquivo para referência
  fs.writeFileSync(path.join(rootDir, 'public', 'splash', 'splash-links.html'), html);
  console.log('✅ Gerado: arquivo HTML com links para splash screens');
}

// Executar a função principal
generateSplashScreens(); 